<template>
	<div class="upload-bg">
		<el-upload
			:class="{ 'avatar-uploader': !imageUrl }"
			:action="uploadUrl"
			:show-file-list="false"
			:on-success="handleAvatarSuccess"
			:before-upload="beforeAvatarUpload"
			:headers="{ [useUserStoreObj.tokenName]: useUserStoreObj.token }"
			:disabled="disabled"
		>
			<template v-if="imageUrl" #trigger>
				<div
					class="img-upload2"
					:style="{
						backgroundImage: `url(${imageUrl})`,
						backgroundSize: `${Number(imgWidth) / Number(imgHeight) < 1 ? 'auto 90%' : '90% auto'}`
					}"
				></div>
			</template>
			<el-icon v-if="!imageUrl" class="avatar-uploader-icon">
				<Plus />
			</el-icon>
		</el-upload>
		<div class="tip">
			<div v-if="imgWidth && imgHeight">图片尺寸：{{ `${imgWidth}*${imgHeight}` }}px</div>
			<div>图片大小：不超过{{ size }}M</div>
			<div>图片格式：JPG、JPEG、PNG、GIF</div>
			<!-- 备注 -->
			<slot name="remark"></slot>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormProps, UploadProps } from 'element-plus';
import base from '/@/utils/request/base';

import { useUserStore } from '/@/store/modules/user';
import { loadingStore } from '/@/store/modules/loading';

const loadingStoreObj = loadingStore();
const useUserStoreObj = useUserStore();
const uploadUrl = base.baseurl + '/shiseido/data/image';
// const uploadUrl = base.baseurl + "/img/uploadImage"
const showTooltip = ref(false);
const props = defineProps({
	imageUrl: {
		type: String,
		default: ''
	},
	imgTip: {
		type: String,
		default: ''
	},
	imgWidth: {
		type: String,
		default: ''
	},
	imgHeight: {
		type: String,
		default: ''
	},
	index: {
		type: Number,
		default: 0
	},
	ind: {
		type: Number,
		default: 0
	},
	size: {
		type: Number,
		default: 0
	},
	disabled: {
		type: Boolean,
		default: false
	}
});
const emits = defineEmits(['handleAvatarSuccess', 'beforeAvatarUpload']);
const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
	loadingStoreObj.hideLoading();
	console.log(props.index, 'props.index');
	emits('handleAvatarSuccess', {
		res: response,
		index: props.index,
		serialNum: props.ind
	});
	// props.imageUrl = URL.createObjectURL(uploadFile.raw!)
};

const beforeAvatarUpload: UploadProps['beforeUpload'] = (file) => {
	loadingStoreObj.showLoading();

	// 添加文件大小校验
	if (props.size > 0) {
		const isLtSize = file.size / 1024 / 1024 <= props.size;
		if (!isLtSize) {
			ElMessage.error(`图片大小不能超过${props.size}MB!`);
			loadingStoreObj.hideLoading();
			return false;
		}
	}
	const isSize = new Promise((resolve, reject) => {
		const width = props.imgWidth;
		const height = props.imgHeight;
		const _URL = window.URL || window.webkitURL;
		const img = new Image();
		const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg' || file.type === 'image/gif';
		if (!isJpgOrPng) {
			ElMessage.error('仅支持上传jpg,png,jpeg,gif格式的图片!');
			loadingStoreObj.hideLoading();
			return Promise.reject();
		}
		if (width && height) {
			img.onload = function () {
				console.log(img.width, img.height, 'img.width, img.height');
				const valid = img.width === Number(width) && img.height === Number(height);
				console.log(valid, 'valid');
				// eslint-disable-next-line prefer-promise-reject-errors
				if (valid) {
					resolve();
				} else {
					reject();
				}
			};
		} else {
			resolve();
		}
		img.src = _URL.createObjectURL(file);
	}).then(
		() => file,
		() => {
			ElMessage.error('上传的图片尺寸不符合规格，请重新上传');
			loadingStoreObj.hideLoading();
			// eslint-disable-next-line prefer-promise-reject-errors
			return Promise.reject();
			// eslint-disable-next-line no-unreachable
			return false;
		}
	);
	return isSize;
};
</script>

<style scoped lang="less">
.upload-bg {
	// width: 115px;
	height: 120px;
	box-sizing: border-box;
	text-align: center;
	position: relative;
	display: flex;
}

.tip {
	margin-left: 130px;
	font-size: 12px;
	text-align: left;
	padding: 10px 0;
}

::v-deep.avatar-uploader .el-upload {
	border: 3px dashed var(--el-border-color);
	border-radius: 6px;
	cursor: pointer;
	overflow: hidden;
	transition: var(--el-transition-duration-fast);
	width: 100%;
	height: 100%;
	display: block;
	margin: 0 auto;
	position: relative;
}

::v-deep.el-icon.avatar-uploader-icon {
	font-size: 23px;
	width: 115px;
	height: 115px;
	text-align: center;
}

::v-deep.avatar-uploader .avatar {
	width: 115px;
	height: 115px;
	display: block;
}

.img-upload2 {
	position: absolute;
	width: 115px;
	height: 115px;
	top: 0;
	left: 0;
	border-radius: 5px;
	background-color: #575757;
	background-repeat: no-repeat;
	background-position: center;
}

.img-btn {
	text-align: center;
	padding: 0;
	position: absolute;
	z-index: 111;
	bottom: 0;
	left: 30px;
	font-size: 12px;
	width: 50px;
	transform: scale(0.8);
}

.avatar-uploader {
	position: absolute;
}
</style>
