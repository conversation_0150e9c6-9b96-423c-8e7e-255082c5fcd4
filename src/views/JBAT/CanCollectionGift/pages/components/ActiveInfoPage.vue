<template>
	<div class="">
		<a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
			<a-form-item label="活动名称" name="name">
				<a-input v-model:value="formData.name" class="with400" :maxlength="15" />
			</a-form-item>
			<a-form-item label="活动时间" name="rangeDate">
				<a-range-picker
					v-model:value="formData.rangeDate"
					show-time
					format="YYYY-MM-DD HH:mm:ss"
					valueFormat="YYYY-MM-DD HH:mm:ss"
					:disabled-date="disabledDate"
					:disabled="[canEdit, false]"
				/>
			</a-form-item>
			<a-form-item label="活动类型">
				<a-radio-group v-model:value="formData.orderType" :disabled="canEdit">
					<a-radio :value="0">集罐</a-radio>
					<a-radio :value="1">满额</a-radio>
				</a-radio-group>
			</a-form-item>
			<a-form-item label="订单状态">
				<a-radio-group v-model:value="formData.orderStatus">
					<!-- <a-radio :value="1">已付款</a-radio> -->
					<a-radio :value="0">已完成</a-radio>
				</a-radio-group>
			</a-form-item>
			<a-form-item label="下单时间" name="orderRangeDate">
				<a-range-picker
					v-model:value="formData.orderRangeDate"
					show-time
					format="YYYY-MM-DD HH:mm:ss"
					valueFormat="YYYY-MM-DD HH:mm:ss"
					:disabled="[canEdit, false]"
					:disabled-date="disabledOrderDate"
				/>
			</a-form-item>
			<a-form-item label="订单延迟天数" name="orderFinishDays">
				确认收货
				<a-input-number v-model:value="formData.orderFinishDays" :min="0" :precision="0" class="with200" :maxlength="5" :disabled="canEdit" />
				天后计入订单
			</a-form-item>
			<a-form-item label="上传商品" name="productList">
				<a-button @click="downloadTemplate">下载模版</a-button>
				<!-- <a-upload>
					<a-button>点击上传</a-button>
				</a-upload> -->
				<div style="margin-top: 10px">
					<el-upload
						ref="upload"
						:limit="1"
						:multiple="false"
						:http-request="handleUpload"
						:headers="uploadHeaders"
						:before-upload="beforeUpload"
						:on-remove="handleRemove"
						:on-exceed="handleExceed"
						accept=".xlsx,.xls"
						class="hide-input"
					>
						<a-button type="primary">{{ formData.productList && formData.productList.length > 0 ? '重新选择' : '选择文件' }}</a-button>
						<template #tip>
							<div class="el-upload__tip">只能上传Excel文件，最多支持5000条记录</div>
						</template>
					</el-upload>
					<a-button v-if="formData.productList && formData.productList.length > 0" type="link" @click="showSkuListModal" style="margin-top: 10px">查看已上传数据</a-button>
				</div>
			</a-form-item>

			<a-form-item label="阶梯设置" name="stepList" v-if="!canEdit">
				<a-button @click="addStep">添加阶梯</a-button>
			</a-form-item>

			<a-form-item :label="`阶梯${index + 1}设置`" v-for="(item, index) in formData.stepList" :key="index">
				<a-form-item label="阶梯名称" :name="['stepList', index, 'stairsName']" :rules="{ required: true, message: '请输入阶梯名称', trigger: 'change' }">
					<a-input v-model:value="item.stairsName" class="with400" maxLength="7" />
				</a-form-item>
				<a-form-item
					v-if="formData.orderType === 0"
					label="阶梯罐数"
					:name="['stepList', index, 'stairsCrowd']"
					:rules="[
						{ required: true, message: '请输入阶梯罐数', trigger: 'change' },
						{
							validator: (rule, value, callback) => {
								if (!value) {
									callback();
									return;
								}
								// 检查当前阶梯罐数是否与其他阶梯重复
								const duplicateIndex = formData.stepList.findIndex((step, stepIndex) => stepIndex !== index && step.stairsCrowd === value);
								if (duplicateIndex !== -1) {
									callback(new Error(`阶梯罐数不能与第${duplicateIndex + 1}个阶梯相同`));
								} else {
									// 当前字段校验通过后，触发其他相关字段重新校验
									nextTick(() => {
										formData.stepList.forEach((step, stepIndex) => {
											if (stepIndex !== index && step.stairsCrowd) {
												formRef.value?.validateFields([['stepList', stepIndex, 'stairsCrowd']]);
											}
										});
									});
									callback();
								}
							},
							trigger: 'change'
						}
					]"
				>
					<a-input-number v-model:value="item.stairsCrowd" :min="0" :precision="0" :maxlength="7" class="with400" @change="validateAllStairsCrowd(index)" />
				</a-form-item>
				<a-form-item
					v-else-if="formData.orderType === 1"
					label="阶梯金额"
					:name="['stepList', index, 'stairsCrowd']"
					:rules="[
						{ required: true, message: '请输入阶梯金额', trigger: 'change' },
						{
							validator: (rule, value, callback) => {
								if (!value) {
									callback();
									return;
								}
								// 检查当前阶梯金额是否与其他阶梯重复
								const duplicateIndex = formData.stepList.findIndex((step, stepIndex) => stepIndex !== index && step.stairsCrowd === value);
								if (duplicateIndex !== -1) {
									callback(new Error(`阶梯金额不能与第${duplicateIndex + 1}个阶梯相同`));
								} else {
									// 当前字段校验通过后，触发其他相关字段重新校验
									nextTick(() => {
										formData.stepList.forEach((step, stepIndex) => {
											if (stepIndex !== index && step.stairsCrowd) {
												formRef.value?.validateFields([['stepList', stepIndex, 'stairsCrowd']]);
											}
										});
									});
									callback();
								}
							},
							trigger: 'change'
						}
					]"
				>
					<a-input-number v-model:value="item.stairsCrowd" :min="0" :precision="0" :maxlength="7" class="with400" @change="validateAllStairsCrowd(index)" />
				</a-form-item>
				<a-form-item label="是否显示" v-if="canEdit" :name="['stepList', index, 'notShow']" :rules="{ required: true, message: '请选择是否显示', trigger: 'change' }">
					<a-radio-group v-model:value="item.notShow" @change="() => validateStepListVisibility()">
						<a-radio :value="0">显示</a-radio>
						<a-radio :value="1">不显示</a-radio>
					</a-radio-group>
				</a-form-item>
				<a-form-item label="领取限制" :name="['stepList', index, 'receiveLimit']" :rules="{ required: true, message: '请选择领取限制', trigger: 'change' }">
					<a-radio-group v-model:value="item.receiveLimit" :disabled="canEdit" @change="(e) => handleReceiveLimitChange(index, e.target.value)">
						<a-radio :value="1">单次领取</a-radio>
						<a-radio :value="2">可重复领取</a-radio>
					</a-radio-group>
				</a-form-item>
				<a-form-item
					v-if="item.receiveLimit === 3"
					label="最多可领取次数"
					:name="['stepList', index, 'maxDrawTimes']"
					:rules="{ required: true, message: '请输入最多可领取次数', trigger: 'change' }"
				>
					<a-input-number
						v-model:value="item.maxDrawTimes"
						:disabled="canEdit"
						:min="1"
						:maxlength="4"
						:precision="0"
						class="with400"
						@change="(value) => updateMaxDrawTimes(index, value)"
					/>
				</a-form-item>

				<a-form-item label="奖品设置">
					<a-button @click="addPrize(index, item)" style="margin-bottom: 10px">添加奖品</a-button>
					<a-table :columns="columns" :data-source="item.prizeList" :pagination="false" bordered>
						<template #bodyCell="{ column, index: rowIndex }">
							<template v-if="column.key === 'prizeName'">
								<a-form-item :name="['stepList', index, 'prizeList', rowIndex, 'prizeName']" :rules="{ required: true, message: '请输入奖品名称', trigger: 'change' }">
									<a-input v-model:value="item.prizeList[rowIndex].prizeName" :maxlength="15" />
								</a-form-item>
							</template>
							<template v-if="column.key === 'prizeType'">
								<a-form-item :name="['stepList', index, 'prizeList', rowIndex, 'prizeType']" :rules="{ required: true, message: '请选择奖品类型', trigger: 'change' }">
									<a-select
										v-model:value="item.prizeList[rowIndex].prizeType"
										style="width: 100%"
										:disabled="canEdit && !!item.prizeList[rowIndex].prizeId"
										@change="
											(value) => {
												if (value === 0) {
													item.prizeList[rowIndex].maxDrawTimes = 1;
												}
											}
										"
									>
										<a-select-option :value="3">实物</a-select-option>
										<a-select-option :value="0">优惠券</a-select-option>
									</a-select>
								</a-form-item>
								<a-form-item
									v-if="item.prizeList[rowIndex].prizeType === 0"
									:name="['stepList', index, 'prizeList', rowIndex, 'prizeKey']"
									:rules="{ required: true, message: '请输入优惠券ID', trigger: 'change' }"
									style="margin-bottom: 0"
								>
									<a-input
										v-if="item.prizeList[rowIndex].prizeType === 0"
										v-model:value="item.prizeList[rowIndex].prizeKey"
										placeholder="请输入优惠券ID"
										:disabled="canEdit && !!item.prizeList[rowIndex].prizeId"
									/>
								</a-form-item>
								<a-form-item
									v-if="item.prizeList[rowIndex].prizeType === 0"
									:name="['stepList', index, 'prizeList', rowIndex, 'prizeProductId']"
									:rules="{ required: true, message: '请输入可使用的SKU', trigger: 'change' }"
									style="margin-bottom: 0"
								>
									<a-input v-model:value="item.prizeList[rowIndex].prizeProductId" placeholder="请输入可使用的SKU" />
								</a-form-item>
							</template>
							<template v-if="column.key === 'maxDrawTimes'">
								<a-form-item :name="['stepList', index, 'prizeList', rowIndex, 'maxDrawTimes']" :rules="{ required: true, message: '请输入单个奖品最大领取次数', trigger: 'change' }">
									<a-input-number
										:precision="0"
										:min="1"
										:max="9999"
										v-model:value="item.prizeList[rowIndex].maxDrawTimes"
										style="width: 100%"
										:disabled="item.prizeList[rowIndex].prizeType === 0"
									/>
								</a-form-item>
							</template>
							<template v-if="column.key === 'prizeTotalNum'">
								<a-form-item :name="['stepList', index, 'prizeList', rowIndex, 'prizeTotalNum']" :rules="{ required: true, message: '请输入奖品总数量', trigger: 'change' }">
									<a-input-number :precision="0" :min="0" :maxlength="5" v-model:value="item.prizeList[rowIndex].prizeTotalNum" />
								</a-form-item>
							</template>
							<template v-if="column.key === 'prizePicture'">
								<a-form-item :name="['stepList', index, 'prizeList', rowIndex, 'prizePicture']" :rules="{ required: true, message: '请上传奖品图', trigger: 'change' }">
									<MyUpload
										@handleAvatarSuccess="
											(data) => {
												item.prizeList[rowIndex].prizePicture = data.res.data;
												formRef.validate([['stepList', index, 'prizeList', rowIndex, 'prizePicture']]);
											}
										"
										:imageUrl="item.prizeList[rowIndex].prizePicture"
										:size="1"
										class="hide-input"
									>
										<template #remark>
											<div>图片尺寸建议 宽750</div>
										</template>
									</MyUpload>
								</a-form-item>
							</template>
							<template v-if="column.key === 'action'">
								<a-button type="link" danger @click="removePrize(index, rowIndex)">删除</a-button>
							</template>
						</template>
					</a-table>
				</a-form-item>

				<a-button type="link" @click="removeStep(index)" style="position: absolute; right: 10px; top: 5px" v-if="!canEdit">删除</a-button>
			</a-form-item>
			<a-form-item label="曝光商品" name="showASkuIds">
				<a-textarea v-model:value="formData.showASkuIds" :autoSize="{ minRows: 4, maxRows: 8 }" />
			</a-form-item>
			<a-form-item label="活动规则" name="rule">
				<a-textarea v-model:value="formData.rule" :autoSize="{ minRows: 4, maxRows: 8 }" />
			</a-form-item>
		</a-form>

		<!-- 添加SKU列表弹窗 -->
		<a-modal v-model:visible="skuListModalVisible" title="商品列表" width="800px" :footer="null">
			<a-table :columns="skuColumns" :data-source="formData.productList" :pagination="false" :scroll="{ y: 400 }" bordered>
				<template #bodyCell="{ column, text }">
					<span style="display: block; text-align: center">{{ text }}</span>
				</template>
			</a-table>
		</a-modal>
	</div>
</template>

<script lang="ts" setup>
import { effect, ref, watch, computed, nextTick, watchEffect } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import MyUpload from '/@/components/MyUpload.vue';
import { useRoute } from 'vue-router';
import { dayjs } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import { exportSkuTemplate, importSkuExcel } from '/@/utils/request/api/JBAT/canCollectionGiftApi';
import { downloadExcel } from '/@/utils';

const route = useRoute();
const props = defineProps(['actData', 'defaultActData']);

const formRef = ref();

const isEdit = ref(route.query.type === 'edit');
const canEdit = ref(false);
const isBeforeStart = ref(true);

const checkActStatus = () => {
	// 添加更完善的安全检查
	if (!props.actData || !props.actData.rangeDate || !Array.isArray(props.actData.rangeDate) || !props.actData.rangeDate[0]) {
		canEdit.value = false;
		isBeforeStart.value = true;
		return;
	}

	try {
		const now = dayjs();
		const startTime = dayjs(props.actData.rangeDate[0]);

		// 检查日期是否有效
		if (!startTime.isValid()) {
			canEdit.value = false;
			isBeforeStart.value = true;
			return;
		}

		isBeforeStart.value = now.isBefore(startTime);
		canEdit.value = isEdit.value && !isBeforeStart.value;
	} catch (error) {
		console.error('checkActStatus error:', error);
		canEdit.value = false;
		isBeforeStart.value = true;
	}
};

const disabledDate = (current) => {
	return current && current < dayjs().startOf('day');
};

const columns = [
	{
		title: '奖品名称',
		dataIndex: 'prizeName',
		key: 'prizeName'
	},
	{
		title: '奖品类型',
		dataIndex: 'prizeType',
		key: 'prizeType'
	},
	{
		title: '单个奖品最大领取次数',
		dataIndex: 'maxDrawTimes',
		key: 'maxDrawTimes'
	},
	{
		title: '奖品总数量',
		dataIndex: 'prizeTotalNum',
		key: 'prizeTotalNum'
	},
	{
		title: '奖品图',
		dataIndex: 'prizePicture',
		key: 'prizePicture'
	},
	{
		title: '操作',
		key: 'action',
		width: '80px'
	}
];
const validateAllStairsCrowd = (changedIndex) => {
	nextTick(() => {
		formData.value.stepList.forEach((step, index) => {
			if (index !== changedIndex && step.stairsCrowd) {
				formRef.value?.validateFields([['stepList', index, 'stairsCrowd']]);
			}
		});
	});
};
const rules: Record<string, Rule[]> = {
	name: [{ required: true, message: '请输入活动名称', trigger: 'change' }],
	rangeDate: [
		{ required: true, message: '请选择活动时间', trigger: 'change' },
		{
			validator: (rule, value) => {
				// 编辑模式下检查时间是否只延长不缩短
				if (isEdit.value && originalActData.value && originalActData.value.rangeDate) {
					const originalStartTime = dayjs(originalActData.value.rangeDate[0]);
					const originalEndTime = dayjs(originalActData.value.rangeDate[1]);
					const newStartTime = dayjs(value[0]);
					const newEndTime = dayjs(value[1]);

					// 开始时间不能变更
					if (!newStartTime.isSame(originalStartTime)) {
						return Promise.reject('编辑模式下不能修改活动开始时间');
					}

					// 结束时间只能延长不能缩短
					if (newEndTime.isBefore(originalEndTime)) {
						return Promise.reject('编辑模式下活动结束时间只能延长不能缩短');
					}
				}
				return Promise.resolve();
			},
			trigger: 'change'
		}
	],
	orderFinishDays: [{ required: true, message: '请输入订单延迟天数', trigger: 'change' }],
	orderRangeDate: [
		{
			validator: (rule, value) => {
				if (!value || value.length !== 2 || !value[0] || !value[1]) {
					return Promise.reject('请选择下单时间');
				}
				return Promise.resolve();
			},
			trigger: 'change'
		},
		{
			validator: (rule, value) => {
				if (value && value.length === 2 && formData.value.rangeDate && formData.value.rangeDate.length === 2) {
					const orderEndTime = dayjs(value[1]);
					const activityEndTime = dayjs(formData.value.rangeDate[1]);
					if (orderEndTime.isAfter(activityEndTime)) {
						return Promise.reject('下单时间结束日期必须小于活动时间结束日期');
					}
				}
				return Promise.resolve();
			},
			trigger: 'change'
		},
		{
			validator: (rule, value) => {
				// 编辑模式下检查时间是否只延长不缩短
				if (isEdit.value && originalActData.value && originalActData.value.orderRangeDate) {
					const originalStartTime = dayjs(originalActData.value.orderRangeDate[0]);
					const originalEndTime = dayjs(originalActData.value.orderRangeDate[1]);
					const newStartTime = dayjs(value[0]);
					const newEndTime = dayjs(value[1]);

					// 开始时间不能变更
					if (!newStartTime.isSame(originalStartTime)) {
						return Promise.reject('编辑模式下不能修改下单开始时间');
					}

					// 结束时间只能延长不能缩短
					if (newEndTime.isBefore(originalEndTime)) {
						return Promise.reject('编辑模式下下单结束时间只能延长不能缩短');
					}
				}
				return Promise.resolve();
			},
			trigger: 'change'
		}
	],
	stepList: [
		{
			validator: (rule, value) => {
				if (!value || value.length === 0) {
					return Promise.reject('请至少添加一条阶梯数据');
				}
				// 只在编辑模式下检查是否至少有一条阶梯是显示状态
				if (isEdit.value) {
					const hasVisibleStep = value.some((step) => step.notShow === 0);
					if (!hasVisibleStep) {
						return Promise.reject('阶梯列表中至少要有一条数据是显示状态');
					}
				}
				return Promise.resolve();
			},
			trigger: 'blur'
		},
		{
			validator: (rule, value) => {
				// 只在编辑模式下检查是否至少有一条阶梯数据处于选中显示状态
				if (isEdit.value) {
					const hasVisibleStep = value.some((step) => step.notShow === 0);
					if (!hasVisibleStep) {
						return Promise.reject('请至少选中一条阶梯数据显示');
					}
				}
				return Promise.resolve();
			},
			trigger: ['change', 'blur']
		}
	],
	productList: [
		{
			validator: (rule, value) => {
				const hasPrizeData = formData.value.stepList.some(
					(step) => step.prizeList && step.prizeList.length > 0 && step.prizeList.some((prize) => prize.prizeName && prize.prizeName.trim() !== '')
				);
				if (hasPrizeData) {
					return Promise.resolve();
				}
				if (!value || value.length === 0 || formData.value.productList.length === 0) {
					return Promise.reject('请上传商品');
				}
				return Promise.resolve();
			},
			trigger: ['change', 'blur']
		}
	],
	otherPictureName: [{ required: true, message: '请输入其他照片名称', trigger: 'change' }],
	rule: [{ required: true, message: '请输入活动规则', trigger: 'change' }],
	showASkuIds: [
		{ required: true, message: '请输入曝光商品ID，多个请用英文逗号分隔', trigger: 'change' },
		{
			validator: (rule, value) => {
				if (value && value.includes('，')) {
					return Promise.reject('请使用英文逗号(,)分隔多个商品ID，不要使用中文逗号');
				}
				return Promise.resolve();
			},
			trigger: 'change'
		},
		{
			validator: (rule, value) => {
				if (value) {
					const ids = value.split(',').filter((id) => id.trim());
					if (ids.length > 100) {
						return Promise.reject('曝光商品ID数量不能超过100个');
					}
				}
				return Promise.resolve();
			},
			trigger: 'change'
		}
	]
};
const originalActData = ref(null);
const defaultPrize = {
	prizeId: '', // 新增字段
	prizeType: 3, // 默认类型为实物
	maxDrawTimes: 1, // 默认单个奖品最大领取次数为1
	prizeProductId: '', // 可使用的SKU，当prizeType为0(优惠券)时使用
	prizeKey: '',
	prizeName: '',
	prizeTotalNum: '',
	prizePicture: ''
};
const defaultStep = {
	stairsId: '', // 新增字段
	stairsName: '', // 对应原 stepName
	stairsCrowd: '', // 对应原 stepNum
	maxDrawTimes: 1, // 对应原 maxReceiveTimes
	notShow: 0, // 新增字段：是否显示，1-显示，0-不显示
	receiveLimit: 1, // 默认单次领取
	prizeList: [{ ...defaultPrize }]
};

const formData = ref({
	shopId: '',
	name: '', // 对应 API 的 activityName
	rangeDate: ['', ''] as [string, string], // 对应 API 的 startTime 和 endTime
	orderRangeDate: ['', ''] as [string, string], // 对应 API 的 orderStartTime 和 orderEndTime
	stepList: [] as any[], // 需要重命名为 stairsList 以匹配 API
	orderStatus: 0, // 对应 API 的 orderStatus
	orderType: 0, // 新增字段
	orderFinishDays: 0, // 新增字段
	rule: '',
	showASkuIds: '', // 新增曝光商品字段，多个采用中文逗号分隔
	// 以下字段可能需要保留用于表单处理
	orderTimeType: 0,
	orderSkuType: 0,
	productList: []
});

const uploadHeaders = ref({
	Authorization: 'Bearer ' + localStorage.getItem('token') || '',
	'Jddz-B-Token': localStorage.getItem('token') || ''
});
const disabledOrderDate = (current) => {
	// 禁用当前日期90天之前的日期
	const ninetyDaysAgo = dayjs().subtract(90, 'day').startOf('day');
	return current && current < ninetyDaysAgo;
};
// 上传前的校验
const beforeUpload = (file) => {
	const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel';
	if (!isExcel) {
		ElMessage.error('只能上传Excel文件！');
		return false;
	}
	return true;
};
const beforeUploadImg = (file: File) => {
	console.log(file.size);
	// 限制文件大小为1MB
	const isLt1M = file.size / 1024 / 1024 < 1;
	if (!isLt1M) {
		ElMessage.error('图片大小不能超过1MB!');
		return false;
	}
	return true;
};
const addPrize = (index, item) => {
	// 限制最多10个奖品
	if (item.prizeList.length >= 10) {
		ElMessage.warning('每个阶梯最多只能添加10个奖品');
		return;
	}
	item.prizeList.push({ ...defaultPrize });
	console.log(item.prizeList);
};
// 添加SKU列表弹窗相关变量
const skuListModalVisible = ref(false);
const skuColumns = computed(() => {
	const columns = [
		{
			title: '商品ID',
			dataIndex: 'productId',
			key: 'productId',
			align: 'center'
		}
	];

	// 只有在活动类型为集罐(0)时才添加罐数列
	if (formData.value.orderType === 0) {
		columns.push({
			title: '罐数',
			dataIndex: 'cans',
			key: 'cans',
			align: 'center'
		});
	}

	return columns;
});

// 显示SKU列表弹窗
const showSkuListModal = () => {
	if (formData.value.productList && formData.value.productList.length > 0) {
		skuListModalVisible.value = true;
	} else {
		ElMessage.warning('暂无上传数据');
	}
};

// 自定义上传方法
const handleUpload = async (options) => {
	try {
		const uploadData = new FormData();
		uploadData.append('file', options.file);
		uploadData.append('type', formData.value.orderType);
		const res: any = await importSkuExcel(uploadData);

		// 检查上传的记录数量
		if (res && res.length > 5000) {
			ElMessage.error('上传失败：商品记录数量超过5000条限制');
			options.onError(new Error('商品记录数量超过5000条限制'));
			return;
		}

		ElMessage.success('上传成功');
		formData.value.productList = res;
		// 使用nextTick确保数据更新后再进行校验
		nextTick(() => {
			formRef.value?.validateFields(['productList']);
		});
		// 上传成功后自动打开弹窗显示数据
		showSkuListModal();
	} catch (error: any) {
		options.onError(error);
	}
};

// 文件移除后的处理
const handleRemove = () => {
	formData.value.productList = [];
	// 触发表单验证
	nextTick(() => {
		formRef.value?.validateFields(['productList']);
	});
};

// 超出文件数量限制的处理
const handleExceed = () => {
	ElMessage.warning(`只能上传一个文件，请先删除已上传的文件`);
};

// 清除sku列表
const clearskuList = () => {
	formData.value.productList = [];
	ElMessage.success('已清除上传的文件和barcode列表');
};

// 下载sku上传模板
const downloadTemplate = async () => {
	exportSkuTemplate(formData.value.orderType).then((data: any) => {
		downloadExcel(data.data, 'sku模板');
	});
};

const onSubmit = async () => {
	try {
		// 先进行基础表单校验
		await formRef.value.validate();

		// 添加显式验证：只在编辑模式下检查是否至少有一条阶梯数据处于选中显示状态
		if (isEdit.value) {
			const hasVisibleStep = formData.value.stepList.some((step) => step.notShow === 0);
			if (!hasVisibleStep) {
				ElMessage.error('请至少选中一条阶梯数据显示');
				return Promise.reject(new Error('请至少选中一条阶梯数据显示'));
			}
		}

		return Promise.resolve(formData.value);
	} catch (error) {
		return Promise.reject(error);
	}
};
const getFormData = () => {
	return formData.value;
};
checkActStatus();

// 添加一个函数来同步 maxDrawTimes 和 receiveLimit 的值
const syncMaxDrawTimesAndReceiveLimit = () => {
	if (formData.value.stepList && formData.value.stepList.length > 0) {
		formData.value.stepList.forEach((step) => {
			// 根据 maxDrawTimes 设置 receiveLimit
			if (step.maxDrawTimes > 1) {
				step.receiveLimit = 2; // 如果 maxDrawTimes 大于 1，设置为可重复领取
			} else if (step.receiveLimit === 1) {
				step.maxDrawTimes = 1;
			} else if (step.receiveLimit === 2 && (!step.maxDrawTimes || step.maxDrawTimes === 1)) {
				step.maxDrawTimes = 999;
			}
		});
	}
};

// 在 effect 钩子中调用这个函数
// 添加一个标志变量，用于控制是否应该响应props.actData的变化
const isInitialized = ref(false);

// 修改effect钩子
effect(() => {
	if (props.actData && !isInitialized.value) {
		originalActData.value = {
			rangeDate: [...(props.actData.rangeDate || ['', ''])],
			orderRangeDate: [...(props.actData.orderRangeDate || ['', ''])]
		};
		// 只在初始化时设置formData
		formData.value = {
			...formData.value,
			shopId: props.actData.shopId,
			name: props.actData.name,
			rangeDate: props.actData.rangeDate || ['', ''],
			orderRangeDate: props.actData.orderRangeDate || ['', ''],
			rule: props.actData.rule,
			orderStatus: props.actData.orderStatus || 0,
			orderType: props.actData.orderType || 0,
			orderFinishDays: props.actData.orderFinishDays || 0,
			showASkuIds: props.actData.showASkuIds || '',
			orderTimeType: props.actData.orderTimeType || 0,
			orderSkuType: props.actData.orderSkuType || 0,
			productList: props.actData.productList || []
		}; // 确保这里有分号

		if (props.actData.stepList && Array.isArray(props.actData.stepList)) {
			formData.value.stepList = props.actData.stepList.map((stair) => ({
				stairsId: stair.stairsId,
				stairsName: stair.stairsName,
				stairsCrowd: stair.stairsCrowd,
				maxDrawTimes: stair.maxDrawTimes,
				notShow: stair.notShow !== undefined ? stair.notShow : 0,
				receiveLimit: stair.receiveLimit !== undefined ? stair.receiveLimit : 1,
				prizeList: stair.prizeList || []
			}));

			syncMaxDrawTimesAndReceiveLimit();
		}

		// 标记已初始化
		isInitialized.value = true;
	}
});
// 添加一个验证阶梯数据是否完整的函数
const validateStepData = (step) => {
	// 验证基本字段
	if (!step.stairsName || !step.stairsCrowd) {
		return false;
	}

	// 验证奖品列表
	if (!step.prizeList || step.prizeList.length === 0) {
		return false;
	}

	// 验证每个奖品的必填字段
	for (const prize of step.prizeList) {
		if (!prize.prizeName || prize.prizeTotalNum === undefined || prize.prizeTotalNum === '') {
			return false;
		}
	}

	return true;
};

// 处理领取限制变更
const handleReceiveLimitChange = (stepIndex, value) => {
	if (value === 1) {
		// 单次领取，设置 maxDrawTimes 为 1
		formData.value.stepList[stepIndex].maxDrawTimes = 1;
	} else if (value === 2) {
		// 可重复领取，设置 maxDrawTimes 为 999
		formData.value.stepList[stepIndex].maxDrawTimes = 999;
	}
};

// 修改 addStep 函数，确保新添加的阶梯默认值正确
const addStep = () => {
	// 限制最多10个阶梯
	if (formData.value.stepList.length >= 10) {
		ElMessage.warning('最多只能添加10个阶梯');
		return;
	}

	// 验证上一条阶梯数据是否已全部填写
	if (formData.value.stepList.length > 0) {
		const lastStep = formData.value.stepList[formData.value.stepList.length - 1];
		if (!validateStepData(lastStep)) {
			ElMessage.warning('请先完成上一条阶梯的所有必填项');
			return;
		}
	}

	// 使用深拷贝创建新的阶梯数据，确保 prizeList 是一个新的数组
	const newStep = {
		...defaultStep,
		stairsName: '', // 使用新字段名
		stairsCrowd: '', // 使用新字段名
		maxDrawTimes: 1, // 默认为1
		notShow: 0, // 默认显示
		receiveLimit: 1, // 默认单次领取
		prizeList: [{ ...defaultPrize }] // 创建新的奖品列表，而不是引用 defaultStep 中的
	};

	formData.value.stepList.push(newStep);

	if (formData.value.stepList.length > 0) {
		formRef.value.clearValidate(['stepList']);
	}
};

// 校验阶梯列表显示状态
const validateStepListVisibility = () => {
	// 只在编辑模式下进行校验
	if (isEdit.value) {
		nextTick(() => {
			formRef.value?.validateFields(['stepList']);
		});
	}
};

// 修改 removeStep 函数，删除阶梯时也要校验
const removeStep = (index: number) => {
	formData.value.stepList.splice(index, 1);
	// 删除后校验是否还有显示的阶梯
	nextTick(() => {
		validateStepListVisibility();
	});
};

const removePrize = (stepIndex: number, prizeIndex: number) => {
	// 确保阶梯和奖品列表存在
	if (formData.value.stepList[stepIndex] && formData.value.stepList[stepIndex].prizeList) {
		// 如果只有一个奖品，显示提示信息
		if (formData.value.stepList[stepIndex].prizeList.length <= 1) {
			ElMessage.warning('每个阶梯至少需要一个奖品');
			return;
		}
		// 删除指定索引的奖品
		formData.value.stepList[stepIndex].prizeList.splice(prizeIndex, 1);
	}
};
// 添加对活动类型的监听，当类型变化时清空上传数据
const upload = ref();
watch(
	() => formData.value.orderType,
	(newVal, oldVal) => {
		if (newVal !== oldVal) {
			// 清空已上传的数据
			formData.value.productList = [];
			// 重置上传组件
			if (upload.value) {
				upload.value.clearFiles();
			}
			ElMessage.success('已切换活动类型，上传数据已清空');

			// 添加这一行：在清空数据后触发表单验证
			nextTick(() => {
				formRef.value?.validateFields(['productList']);
			});
		}
	},
	{ immediate: false }
);
// 在 script 部分添加以下函数（在第 500 行左右，在 addStep 函数之前）

// 更新 maxDrawTimes 时同步更新 receiveLimit
const updateMaxDrawTimes = (stepIndex, value) => {
	if (!canEdit) {
		if (value > 1) {
			// 如果 maxDrawTimes 大于 1，自动设置 receiveLimit 为 2（可重复领取）
			formData.value.stepList[stepIndex].receiveLimit = 2;
		} else {
			formData.value.stepList[stepIndex].receiveLimit = 1;
		}
	}
};
// 添加一个新方法，用于清空商品列表并触发校验
const clearProductList = () => {
	formData.value.productList = [];
	// 重置上传组件
	const uploadRef = document.querySelector('.el-upload__input');
	if (uploadRef) {
		uploadRef.value = '';
	}
	// 触发表单验证
	nextTick(() => {
		formRef.value?.validateFields(['productList']);
	});
};
defineExpose({
	onSubmit,
	getFormData,
	clearProductList
});
</script>

<style scoped lang="scss">
.with400 {
	width: 400px;
}
.with200 {
	width: 200px;
}
</style>
<style>
.hide-input input {
	display: none !important;
}
</style>
