<template>
	<div class="">
		<a-form layout="inline">
			<a-form-item label="店铺ID" class="marg-b-20">
				<a-input v-model:value="formData.shopIds" readonly style="width: 400px" placeholder="请输入店铺ID（多个店铺使用英文逗号分隔）"></a-input>
			</a-form-item>
			<!-- <a-form-item label="日期" class="marg-b-20">
				<a-range-picker :allowClear="false" v-model:value="formData.rangeDate" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" :disabledDate="disabledDate" />
			</a-form-item> -->
			<a-form-item class="marg-b-20">
				<a-button type="primary" style="margin-bottom: 20px" @click="searchAct">搜索</a-button>
				<a-button type="primary" style="margin-bottom: 20px" @click="exportData">导出</a-button>
			</a-form-item>
		</a-form>
		<a-table :columns="dynamicColumns" :data-source="tableData" :pagination="false" bordered :loading="isLoading" :scroll="{ x: '100%' }">
			<!-- <template #bodyCell="{ record, column }"> </template> -->
		</a-table>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { shopBasicData, shopBasicDataExport } from '/@/utils/request/api/JBAT/canCollectionGiftApi';
import { dayjs } from 'element-plus';
import { downloadExcel } from '/@/utils';

const router = useRouter();
const route = useRoute();

const isLoading = ref(false);
const pageInfo = ref({
	pageNo: 1,
	pageSize: 10,
	toTal: 0
});

// 动态列数据
const columnsData = ref([]);

// 计算属性：根据columnsData动态生成表格列
const dynamicColumns = computed(() => {
	// 根据sort字段排序
	return columnsData.value
		.sort((a, b) => a.sort - b.sort)
		.map((item) => ({
			title: item.value,
			dataIndex: item.key,
			key: item.key,
			align: 'center', // 可选：设置列居中对齐
			width: 200, // 设置列宽
			ellipsis: true, // 超出宽度显示省略号
			tooltip: true // 鼠标悬停显示完整内容
		}));
});

// 日期禁用函数 - 限制选择范围为最多三个月
const disabledDate = (current) => {
	// 禁用当前日期及之后的日期
	if (current && current > dayjs().endOf('day')) {
		return true;
	}
	// 禁用三个月前的日期
	if (current && current < dayjs().subtract(3, 'month').startOf('day')) {
		return true;
	}
	return false;
};

const formData = reactive({
	shopIds: '',
	rangeDate: [
		// 默认开始日期为一个月前
		dayjs().subtract(1, 'month'),
		// 默认结束日期为昨天
		dayjs().subtract(1, 'day')
	]
});
const tableData = ref([]);

const getList = async () => {
	try {
		isLoading.value = true;
		const query = {
			shopIds: formData.shopIds,
			startDate: formData.rangeDate[0] ? dayjs(formData.rangeDate[0]).format('YYYY-MM-DD') : '',
			endDate: formData.rangeDate[1] ? dayjs(formData.rangeDate[1]).format('YYYY-MM-DD') : ''
			// pageNo: pageInfo.value.pageNo,
			// pageSize: pageInfo.value.pageSize
		};
		const res: any = await shopBasicData(query);

		// 假设res.columns包含列定义数据
		if (res.columns && Array.isArray(res.columns)) {
			columnsData.value = res.columns;
		}
		// 处理表格数据
		tableData.value = res.dataList; // 根据实际接口返回结构调整
		isLoading.value = false;
	} catch (error) {
		isLoading.value = false;
	}
};

const searchAct = () => {
	pageInfo.value.pageNo = 1;
	getList();
};

const exportData = () => {
	const query = {
		activityId: route.query.id,
		startDate: formData.rangeDate[0] ? dayjs(formData.rangeDate[0]).format('YYYY-MM-DD') : '',
		endDate: formData.rangeDate[1] ? dayjs(formData.rangeDate[1]).format('YYYY-MM-DD') : ''
	};
	shopBasicDataExport(query).then((data: any) => {
		downloadExcel(data.data, '店铺数据');
	});
};

getList();
</script>

<style scoped lang="scss">
.page {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;
}
.shop-select {
	width: 400px;
}
.marg-b-20 {
	margin-bottom: 20px;
}
.label {
	font-size: 20px;
	font-weight: 600;
	margin-bottom: 20px;
}

/* 添加表格横向滚动样式 */
// :deep(.ant-table-body) {
// 	overflow-x: auto !important;
// }
// :deep(.ant-table-cell) {
// 	white-space: nowrap;
// 	overflow: hidden;
// 	text-overflow: ellipsis;
// }
</style>
