<template>
	<div class="zst-main edit-page">
		<a-spin :spinning="isLoading">
			<div class="scroll-container">
				<DecoPage ref="decoRef" :decoData="decoData" v-if="step === 1" />
				<ActiveInfoPage ref="activeInfoRef" :actData="actData" :defaultActData="defaultActData" v-else-if="step === 2" />
			</div>
		</a-spin>

		<div class="edit-page-footer">
			<a-button @click="goBack" v-if="step === 1">取消</a-button>
			<a-button v-if="step !== 1" @click="prevStep">上一步</a-button>
			<a-button type="primary" v-if="step !== 2" @click="nexStep">下一步</a-button>
			<a-button type="primary" v-if="step === 2 && !isEdit" @click="confirmForm">创建活动</a-button>
			<a-button type="primary" v-if="step === 2 && isEdit" @click="confirmForm">保存活动</a-button>
		</div>

		<a-modal :visible="visible" title="确认" :confirm-loading="confirmLoading" @ok="createAct" @cancel="onCancel">
			<p>是否确认创建活动？</p>
		</a-modal>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import DecoPage from './components/DecoPage.vue';
import ActiveInfoPage from './components/ActiveInfoPage.vue';
// import { addActivity, updateActivity, getActivityDetail } from '/@/utils/request/api/JBAT/newCustomerOneApi';
import { addActivity, updateActivity, getActivityDetail } from '/@/utils/request/api/JBAT/canCollectionGiftApi';
import { dayjs } from 'element-plus';
import { ElMessage } from 'element-plus';

const route = useRoute();
const router = useRouter();

const isEdit = ref(route.query.type === 'edit');
const isLoading = ref(false);
const visible = ref(false);
const confirmLoading = ref(false);
const step = ref(1);
const decoRef = ref();
const activeInfoRef = ref();

const HAIPUShopList = [
	{ label: '海普诺凯1897官方旗舰店', value: '37702451' },
	{ label: '能立多旗舰店', value: '40842709' }
];

const defaultData = {
	kvImg: '//img10.360buyimg.com/imgzone/jfs/t1/321444/23/13176/53220/6864cc87F9122dbb5/f185cce6712dc78e.jpg',
	strategyImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/8wnvikrgd75ihyhsf1fdsq48jauuhh29.png',
	linkType: 1
};

const HAiPUDefaultData = {
	kvImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/g2atvujrzpt49cx5z31gk88n3fj067f0.png',
	strategyImg: 'https://ks-cjhd.oss-cn-zhangjiakou.aliyuncs.com//dy-cjtd/image/8wnvikrgd75ihyhsf1fdsq48jauuhh29.png',
	linkType: 1
};

const decoData = ref({
	kvImg: '',
	strategyImg: '',
	speakerImg: '',
	linkType: 1
});
const actData = ref({
	shopId: '',
	name: '',
	rangeDate: [],
	newUserType: 0,
	oldUserType: 0,
	orderTimeType: 0,
	orderSkuType: 0,
	orderFinishDays: 0,
	productList: [],
	rule: '',
	showASkuIds: '' // 新增曝光商品字段
});

const defaultActData = ref({});

const nexStep = async () => {
	try {
		if (step.value === 1) {
			decoData.value = await decoRef.value.onSubmit();
			step.value = 2;
		}
	} catch (error) {
		console.error(error);
	}
};
// 上一步
const prevStep = () => {
	actData.value = activeInfoRef.value.getFormData();
	step.value = step.value - 1;
	console.log('actData', actData.value);
};

const getParams = (): any => {
	// 处理阶梯数据
	const stairsList = actData.value.stepList.map((step) => ({
		stairsId: step.stairsId || '',
		stairsName: step.stairsName || step.stepName, // 兼容旧字段名
		stairsCrowd: step.stairsCrowd || step.stepNum, // 兼容旧字段名
		maxDrawTimes: step.maxDrawTimes || step.maxReceiveTimes || 1, // 兼容旧字段名
		notShow: step.notShow !== undefined ? step.notShow : 0, // 处理 notShow 字段，默认为显示
		// receiveLimit: step.receiveLimit !== undefined ? step.receiveLimit : 1, // 处理 receiveLimit 字段，默认为单次领取
		prizeList: step.prizeList || []
	}));

	return {
		decoData: JSON.stringify(decoData.value),
		shopId: actData.value.shopId,
		activityName: actData.value.name,
		startTime: dayjs(actData.value.rangeDate[0]).format('YYYY-MM-DD HH:mm:ss'),
		endTime: dayjs(actData.value.rangeDate[1]).format('YYYY-MM-DD HH:mm:ss'),
		orderType: actData.value.orderType || 0,
		orderStatus: actData.value.orderStatus || 0,
		orderStartTime: actData.value.orderRangeDate && actData.value.orderRangeDate[0] ? dayjs(actData.value.orderRangeDate[0]).format('YYYY-MM-DD HH:mm:ss') : '',
		orderEndTime: actData.value.orderRangeDate && actData.value.orderRangeDate[1] ? dayjs(actData.value.orderRangeDate[1]).format('YYYY-MM-DD HH:mm:ss') : '',
		orderFinishDays: actData.value.orderFinishDays || 0,
		rule: actData.value.rule,
		showASkuIds: actData.value.showASkuIds || '', // 添加曝光商品字段
		stairsList: stairsList, // 使用新的字段名和结构
		productList: actData.value.productList
	};
};

// 添加临时存储变量
const tempFormData = ref(null);

const createAct = async () => {
	// 只在最终确认时更新actData.value
	if (tempFormData.value) {
		actData.value = tempFormData.value;
	}

	const params = getParams();
	try {
		confirmLoading.value = true;
		if (route.query.type === 'create') {
			params.shopId = route.query.shopId;
			await addActivity(params);
		} else {
			await updateActivity({ activityId: route.query.id, ...params });
		}
		confirmLoading.value = false;
		visible.value = false;
		router.go(-1);
	} catch (error) {
		confirmLoading.value = false;
		console.error(error);
	}
};

const onCancel = () => {
	if (confirmLoading.value) return;
	visible.value = false;
	// 清空上传的 Excel 数据
	// activeInfoRef.value?.clearProductList();
	// 恢复原始数据，避免影响表单状态
	if (defaultActData.value && Object.keys(defaultActData.value).length > 0) {
		actData.value = JSON.parse(JSON.stringify(defaultActData.value));
	}
};

const confirmForm = async () => {
	try {
		// 获取表单数据但不直接赋值给actData.value
		const formData = await activeInfoRef.value.onSubmit();

		// 只在表单验证通过后进行检查
		if (!formData.productList?.length) {
			ElMessage.error('请上传商品');
			return;
		}
		// 显示确认弹窗，但不改变actData.value
		// 只在用户最终确认创建时才更新actData.value
		visible.value = true;

		// 临时存储表单数据，用于createAct函数使用
		tempFormData.value = formData;
	} catch (error) {
		console.error(error);
	}
};

const goBack = () => {
	router.go(-1);
};

// const init = async () => {
// 	if (HAIPUShopList.find((item: any) => item.value === route.query.shopId)) {
// 		decoData.value = JSON.parse(JSON.stringify(HAiPUDefaultData));
// 	} else {
// 		decoData.value = JSON.parse(JSON.stringify(defaultData));
// 	}
// };
const init = async () => {
	if (route.query.type === 'create') {
		if (HAIPUShopList.find((item: any) => item.value === route.query.shopId)) {
			decoData.value = JSON.parse(JSON.stringify(HAiPUDefaultData));
		} else {
			decoData.value = JSON.parse(JSON.stringify(defaultData));
		}
		return;
	}
	try {
		const data = (await getActivityDetail({ activityId: route.query.id, shopId: route.query.shopId })) as any;
		// 处理活动数据
		actData.value = {
			shopId: data.shopId,
			actId: data.id,
			name: data.activityName,
			rangeDate: [data.startTime, data.endTime],
			orderRangeDate: [data.orderStartTime, data.orderEndTime],
			orderType: data.orderType || 0,
			orderStatus: data.orderStatus || 0,
			orderFinishDays: data.orderFinishDays || 0,
			// 处理阶梯数据
			stepList: data.stairsList || [],
			// 保留原有字段
			newUserType: data.newUserType,
			oldUserType: data.oldUserType,
			orderTimeType: data.orderTimeType,
			orderSkuType: data.orderSkuType,
			productList: data.productList,
			newCoupon: data.prizeList?.filter((item: any) => item.prizeType === 1)[0] || {},
			oldCoupon: data.prizeList?.filter((item: any) => item.prizeType === 3)[0] || {},
			rule: data.rule,
			showASkuIds: data.showASkuIds || '' // 添加曝光商品字段
		};
		defaultActData.value = JSON.parse(JSON.stringify(actData.value));

		// 处理装修数据
		decoData.value = typeof data.decoData === 'string' ? JSON.parse(data.decoData) : data.decoData;
	} catch (error) {
		console.error(error);
	}
};
init();
</script>

<style scoped lang="scss">
.edit-page {
	position: relative;
	// background: #f0f2f5;

	.scroll-container {
		height: calc(100vh - 140px);
		overflow: auto;
	}
	.edit-page-footer {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 70px;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #fff;
	}
}
</style>
